# Extension Icons

This directory should contain the following icon files:

- `icon16.png` - 16x16 pixels (toolbar icon)
- `icon32.png` - 32x32 pixels (Windows)
- `icon48.png` - 48x48 pixels (extension management page)
- `icon128.png` - 128x128 pixels (Chrome Web Store)

## Icon Design Guidelines

The icons should:
- Use the Redbook brand colors (#ff2442 as primary)
- Be simple and recognizable at small sizes
- Include a book or reading-related symbol
- Have a clean, modern design

## Temporary Solution

For development purposes, you can use any PNG files with the correct dimensions, or create simple colored squares as placeholders.

## Creating Icons

You can create icons using:
- Online icon generators
- Design tools like Figma, Sketch, or Canva
- Simple graphics editors like GIMP or Paint.NET

The icon should represent the "enhanced reading" concept, possibly combining:
- A book symbol 📖
- The Redbook red color
- Enhancement/improvement indicators (like a plus sign or sparkles)
