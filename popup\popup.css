/**
 * Redbook Enhancer - Popup Styles
 */

* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

body {
  font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", <PERSON><PERSON>, "Helvetica Neue", <PERSON>l, sans-serif;
  background: #f8f9fa;
  color: #333;
  line-height: 1.5;
}

.popup-container {
  width: 320px;
  background: white;
  border-radius: 12px;
  box-shadow: 0 4px 24px rgba(0, 0, 0, 0.1);
  overflow: hidden;
}

/* Header */
.popup-header {
  background: linear-gradient(135deg, #ff2442, #ff6b6b);
  color: white;
  padding: 20px;
  text-align: center;
}

.popup-header h1 {
  font-size: 18px;
  font-weight: 600;
  margin-bottom: 4px;
}

.subtitle {
  font-size: 12px;
  opacity: 0.9;
}

/* Content */
.popup-content {
  padding: 20px;
}

.settings-section {
  margin-bottom: 24px;
}

.settings-section h2 {
  font-size: 14px;
  font-weight: 600;
  color: #666;
  margin-bottom: 12px;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

/* Setting Items */
.setting-item {
  margin-bottom: 16px;
}

.setting-label {
  display: flex;
  align-items: flex-start;
  cursor: pointer;
  padding: 8px;
  border-radius: 8px;
  transition: background-color 0.2s ease;
}

.setting-label:hover {
  background-color: #f8f9fa;
}

.setting-label input[type="checkbox"] {
  display: none;
}

.checkmark {
  width: 20px;
  height: 20px;
  border: 2px solid #ddd;
  border-radius: 4px;
  margin-right: 12px;
  flex-shrink: 0;
  position: relative;
  transition: all 0.2s ease;
}

.setting-label input[type="checkbox"]:checked + .checkmark {
  background-color: #ff2442;
  border-color: #ff2442;
}

.setting-label input[type="checkbox"]:checked + .checkmark::after {
  content: "✓";
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  color: white;
  font-size: 12px;
  font-weight: bold;
}

.setting-info {
  flex: 1;
}

.setting-title {
  display: block;
  font-weight: 500;
  font-size: 14px;
  margin-bottom: 2px;
}

.setting-description {
  display: block;
  font-size: 12px;
  color: #666;
}

/* Mode Buttons */
.mode-buttons {
  display: flex;
  gap: 8px;
}

.mode-btn {
  flex: 1;
  padding: 12px 8px;
  border: 2px solid #e9ecef;
  border-radius: 8px;
  background: white;
  cursor: pointer;
  transition: all 0.2s ease;
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 4px;
}

.mode-btn:hover {
  border-color: #ff2442;
  background-color: #fff5f5;
}

.mode-btn.active {
  border-color: #ff2442;
  background-color: #ff2442;
  color: white;
}

.mode-icon {
  font-size: 20px;
}

.mode-text {
  font-size: 12px;
  font-weight: 500;
}

/* Action Buttons */
.action-buttons {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.action-btn {
  padding: 12px 16px;
  border: none;
  border-radius: 8px;
  background: #ff2442;
  color: white;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
}

.action-btn:hover {
  background: #e01e3c;
  transform: translateY(-1px);
}

.action-btn.secondary {
  background: #6c757d;
}

.action-btn.secondary:hover {
  background: #5a6268;
}

.btn-icon {
  font-size: 16px;
}

/* Footer */
.popup-footer {
  padding: 16px 20px;
  background: #f8f9fa;
  display: flex;
  justify-content: space-between;
  align-items: center;
  border-top: 1px solid #e9ecef;
}

.status-indicator {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 12px;
  color: #666;
}

.status-dot {
  width: 8px;
  height: 8px;
  border-radius: 50%;
  background: #dc3545;
}

.status-dot.active {
  background: #28a745;
}

.version {
  font-size: 11px;
  color: #999;
}

/* Animations */
@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.popup-container {
  animation: fadeIn 0.3s ease;
}

/* Responsive adjustments */
@media (max-width: 350px) {
  .popup-container {
    width: 300px;
  }
  
  .popup-content {
    padding: 16px;
  }
}
