# 测试指南 (Testing Guide)

## 测试清单 (Testing Checklist)

### 基础功能测试

#### ✅ 脚本加载测试
- [ ] 访问 xiaohongshu.com 后脚本自动加载
- [ ] 控制台显示 "🎉 小红书阅读增强器已启用" 消息
- [ ] 页面右上角显示启用通知

#### ✅ 导航栏隐藏功能
- [ ] 顶部"下载App"横幅被隐藏
- [ ] 登录提示弹窗被阻止
- [ ] 注册覆盖层被移除
- [ ] 其他干扰性导航元素被隐藏

#### ✅ 内容展开功能
- [ ] 长文本内容自动完全展开
- [ ] "显示更多"按钮自动点击
- [ ] 图片画廊完整显示
- [ ] 内容高度限制被移除

#### ✅ 阅读体验增强
- [ ] 页面内容居中显示
- [ ] 字体大小和行高优化
- [ ] 卡片样式改进
- [ ] 响应式布局正常工作

### 快捷键测试

#### ✅ W键 - 宽屏模式切换
- [ ] 按W键切换到宽屏模式
- [ ] 内容区域宽度变为100%
- [ ] 再次按W键恢复标准模式
- [ ] 显示相应的通知消息

#### ✅ H键 - 导航栏切换
- [ ] 按H键隐藏导航栏
- [ ] 按H键显示导航栏
- [ ] 设置状态正确保存
- [ ] 显示相应的通知消息

#### ✅ E键 - 内容展开
- [ ] 按E键展开所有内容
- [ ] 页面中的折叠内容被展开
- [ ] 显示"内容已展开"通知

### 兼容性测试

#### ✅ 浏览器兼容性
- [ ] Chrome 浏览器正常工作
- [ ] Firefox 浏览器正常工作
- [ ] Edge 浏览器正常工作
- [ ] Safari 浏览器正常工作（如适用）

#### ✅ 页面类型测试
- [ ] 首页 (xiaohongshu.com) 正常工作
- [ ] 用户主页正常工作
- [ ] 单个帖子页面正常工作
- [ ] 搜索结果页面正常工作
- [ ] 分类页面正常工作

#### ✅ 移动端测试
- [ ] 移动端浏览器正常工作
- [ ] 响应式布局适配正确
- [ ] 触摸操作不受影响
- [ ] 通知位置适配移动端

### 性能测试

#### ✅ 加载性能
- [ ] 脚本加载不影响页面加载速度
- [ ] DOM操作不造成明显延迟
- [ ] 内存使用量在合理范围内

#### ✅ 动态内容处理
- [ ] 滚动加载新内容时脚本正常工作
- [ ] AJAX请求后的内容被正确处理
- [ ] 页面导航后脚本重新生效

### 设置持久化测试

#### ✅ 设置保存
- [ ] 宽屏模式设置在页面刷新后保持
- [ ] 导航栏隐藏设置正确保存
- [ ] 其他配置选项正确保存

### 错误处理测试

#### ✅ 异常情况
- [ ] 页面结构变化时脚本不报错
- [ ] 网络错误时脚本正常工作
- [ ] 与其他脚本/扩展共存无冲突

## 测试步骤

### 1. 安装测试
1. 按照 INSTALL.md 的步骤安装脚本
2. 验证安装成功的标志
3. 检查控制台是否有错误

### 2. 功能测试
1. 逐一测试上述功能清单
2. 记录任何异常行为
3. 验证快捷键在不同页面元素上的表现

### 3. 兼容性测试
1. 在不同浏览器中测试
2. 测试不同的小红书页面类型
3. 验证移动端表现

### 4. 压力测试
1. 长时间使用脚本
2. 快速切换页面
3. 频繁使用快捷键

## 常见问题排查

### 脚本不生效
1. 检查 Tampermonkey 是否启用
2. 确认脚本匹配规则正确
3. 查看控制台错误信息

### 样式异常
1. 检查是否有CSS冲突
2. 验证选择器是否正确
3. 确认页面结构是否变化

### 快捷键冲突
1. 测试在不同页面元素上的表现
2. 检查是否与浏览器快捷键冲突
3. 验证事件监听器是否正确绑定

## 测试报告模板

```
测试日期: [日期]
测试浏览器: [浏览器版本]
测试页面: [页面URL]

功能测试结果:
- 导航栏隐藏: ✅/❌
- 内容展开: ✅/❌  
- 阅读增强: ✅/❌
- 快捷键: ✅/❌

发现的问题:
1. [问题描述]
2. [问题描述]

建议改进:
1. [改进建议]
2. [改进建议]
```
